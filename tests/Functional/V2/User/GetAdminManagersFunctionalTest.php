<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\User;

use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminUsersEndpoints;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

class GetAdminManagersFunctionalTest extends FunctionalTestCase
{
    /**
     * Test the GetManagers endpoint with valid requests.
     */
    #[DataProvider('getManagersSuccessProvider')]
    public function testGetManagersSuccess(
        ?string $search,
        ?int $page,
        ?int $pageSize,
        array $expectedStructure
    ): void {
        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminUsersEndpoints::getManagersEndpoint(
                search: $search,
                page: $page,
                pageSize: $pageSize
            ),
            bearerToken: $userToken
        );

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);

        $responseData = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('data', $responseData);
        $this->assertArrayHasKey('users', $responseData['data']);
        $this->assertArrayHasKey('total', $responseData['data']);

        if (!empty($responseData['data']['users'])) {
            $firstUser = $responseData['data']['users'][0];
            foreach ($expectedStructure as $key) {
                $this->assertArrayHasKey($key, $firstUser);
            }
        }
    }

    /**
     * Test the GetManagers endpoint validation errors.
     */
    #[DataProvider('getManagersErrorProvider')]
    public function testGetManagersValidationErrors(
        array $queryParams,
        int $expectedStatusCode,
        array $expectedViolations
    ): void {
        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminUsersEndpoints::getManagersEndpoint(
                search: $queryParams['search'] ?? null,
                page: isset($queryParams['page']) ? (int) $queryParams['page'] : null,
                pageSize: isset($queryParams['page_size']) ? (int) $queryParams['page_size'] : null
            ),
            bearerToken: $userToken
        );

        $this->assertEquals($expectedStatusCode, $response->getStatusCode());

        // Error case - verify violations
        $responseContent = json_decode($response->getContent(), true);
        $this->assertIsArray($responseContent);
        $this->assertArrayHasKey('metadata', $responseContent);
        $this->assertArrayHasKey('violations', $responseContent['metadata']);

        $violations = $responseContent['metadata']['violations'];
        $this->assertIsArray($violations);

        foreach ($expectedViolations as $field => $expectedMessages) {
            $this->assertArrayHasKey($field, $violations);
            // Each violation is a string, not an array
            foreach ($expectedMessages as $expectedMessage) {
                $this->assertEquals($expectedMessage, $violations[$field]);
            }
        }
    }

    /**
     * Test unauthorized access to GetManagers endpoint.
     */
    public function testGetManagersUnauthorized(): void
    {
        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminUsersEndpoints::getManagersEndpoint()
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public static function getManagersSuccessProvider(): \Generator
    {
        $expectedStructure = ['id', 'email', 'first_name', 'last_name'];

        yield 'No parameters' => [
            'search' => null,
            'page' => null,
            'pageSize' => null,
            'expectedStructure' => $expectedStructure,
        ];

        yield 'With search' => [
            'search' => 'manager',
            'page' => null,
            'pageSize' => null,
            'expectedStructure' => $expectedStructure,
        ];

        yield 'With pagination' => [
            'search' => null,
            'page' => 1,
            'pageSize' => 5,
            'expectedStructure' => $expectedStructure,
        ];

        yield 'With search and pagination' => [
            'search' => 'test',
            'page' => 1,
            'pageSize' => 10,
            'expectedStructure' => $expectedStructure,
        ];
    }

    public static function getManagersErrorProvider(): \Generator
    {
        yield 'Empty search' => [
            'queryParams' => ['search' => ''],
            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
            'expectedViolations' => [
                '[search]' => ['This value should not be blank.'],
            ],
        ];
    }
}
