<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\User;

use App\Entity\User;
use App\Tests\Functional\V2\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminUsersEndpoints;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

class GetAdminManagersFunctionalTest extends FunctionalTestCase
{
    /**
     * Test the GetManagers endpoint with valid requests.
     */
    #[DataProvider('getManagersSuccessProvider')]
    public function testGetManagersSuccess(
        ?string $search,
        ?int $page,
        ?int $pageSize,
        array $expectedStructure
    ): void {
        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminUsersEndpoints::getManagersEndpoint(
                search: $search,
                page: $page,
                pageSize: $pageSize
            ),
            bearerToken: $userToken
        );

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);

        $responseData = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('data', $responseData);
        $this->assertArrayHasKey('users', $responseData['data']);
        $this->assertArrayHasKey('total', $responseData['data']);

        if (!empty($responseData['data']['users'])) {
            $firstUser = $responseData['data']['users'][0];
            foreach ($expectedStructure as $key) {
                $this->assertArrayHasKey($key, $firstUser);
            }

            // Verify that all returned users have ROLE_MANAGER or ROLE_MANAGER_EDITOR
            foreach ($responseData['data']['users'] as $user) {
                $this->assertTrue(
                    in_array(User::ROLE_MANAGER, $user['roles']) || 
                    in_array(User::ROLE_MANAGER_EDITOR, $user['roles']),
                    'User should have ROLE_MANAGER or ROLE_MANAGER_EDITOR role'
                );
                $this->assertTrue($user['isActive'], 'User should be active');
            }
        }
    }

    /**
     * Test the GetManagers endpoint validation errors.
     */
    #[DataProvider('getManagersErrorProvider')]
    public function testGetManagersValidationErrors(
        array $queryParams,
        int $expectedStatusCode,
        array $expectedViolations
    ): void {
        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminUsersEndpoints::getManagersEndpoint(
                search: $queryParams['search'] ?? null,
                page: isset($queryParams['page']) ? (int) $queryParams['page'] : null,
                pageSize: isset($queryParams['page_size']) ? (int) $queryParams['page_size'] : null
            ),
            bearerToken: $userToken
        );

        $this->assertResponseStatusCodeSame($expectedStatusCode);

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('violations', $responseData);

        foreach ($expectedViolations as $field => $expectedMessage) {
            $found = false;
            foreach ($responseData['violations'] as $violation) {
                if ($violation['propertyPath'] === $field && $violation['message'] === $expectedMessage) {
                    $found = true;
                    break;
                }
            }
            $this->assertTrue($found, "Expected violation for field '$field' with message '$expectedMessage' not found");
        }
    }

    /**
     * Test unauthorized access to GetManagers endpoint.
     */
    public function testGetManagersUnauthorized(): void
    {
        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminUsersEndpoints::getManagersEndpoint()
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public static function getManagersSuccessProvider(): \Generator
    {
        $expectedStructure = ['id', 'email', 'firstName', 'lastName', 'roles', 'isActive'];

        yield 'No parameters' => [
            'search' => null,
            'page' => null,
            'pageSize' => null,
            'expectedStructure' => $expectedStructure,
        ];

        yield 'With search' => [
            'search' => 'manager',
            'page' => null,
            'pageSize' => null,
            'expectedStructure' => $expectedStructure,
        ];

        yield 'With pagination' => [
            'search' => null,
            'page' => 1,
            'pageSize' => 5,
            'expectedStructure' => $expectedStructure,
        ];

        yield 'With search and pagination' => [
            'search' => 'test',
            'page' => 1,
            'pageSize' => 10,
            'expectedStructure' => $expectedStructure,
        ];
    }

    public static function getManagersErrorProvider(): \Generator
    {
        yield 'Invalid page type' => [
            'queryParams' => ['page' => 'invalid', 'page_size' => '10'],
            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
            'expectedViolations' => [
                '[page]' => 'This value should be of type digit.',
            ],
        ];

        yield 'Page less than 1' => [
            'queryParams' => ['page' => '0', 'page_size' => '10'],
            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
            'expectedViolations' => [
                '[page]' => 'Page must be greater than 0.',
            ],
        ];

        yield 'Page without page_size' => [
            'queryParams' => ['page' => '1'],
            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
            'expectedViolations' => [
                '[page]' => 'Page size is required when page is provided.',
            ],
        ];

        yield 'Page_size without page' => [
            'queryParams' => ['page_size' => '10'],
            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
            'expectedViolations' => [
                '[page_size]' => 'Page is required when page size is provided.',
            ],
        ];
    }
}
