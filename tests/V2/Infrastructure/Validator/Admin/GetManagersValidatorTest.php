<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Validator\Admin;

use App\Tests\V2\Infrastructure\Validator\ValidatorTestCase;
use App\V2\Infrastructure\Validator\Admin\GetManagersValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use PHPUnit\Framework\Attributes\DataProvider;

class GetManagersValidatorTest extends ValidatorTestCase
{
    /**
     * @throws ValidatorException
     */
    #[DataProvider('validateSuccessProvider')]
    public function testValidateSuccess(array $payload): void
    {
        $this->expectNotToPerformAssertions();

        GetManagersValidator::validateGetManagersRequest($payload);
    }

    #[DataProvider('validateFailProvider')]
    public function testValidateFail(array $payload, array $violations): void
    {
        try {
            GetManagersValidator::validateGetManagersRequest($payload);
            $this->fail('Expected ValidatorException was not thrown');
        } catch (ValidatorException $e) {
            $this->assertViolations($violations, $e->getViolations());
        }
    }

    public static function validateSuccessProvider(): \Generator
    {
        yield 'Empty payload' => [
            'payload' => [],
        ];

        yield 'With search' => [
            'payload' => [
                'search' => 'john',
            ],
        ];

        yield 'With pagination' => [
            'payload' => [
                'page' => '1',
                'page_size' => '10',
            ],
        ];

        yield 'With search and pagination' => [
            'payload' => [
                'search' => 'john',
                'page' => '1',
                'page_size' => '10',
            ],
        ];
    }

    public static function validateFailProvider(): \Generator
    {
        yield 'Invalid search type' => [
            [
                'search' => 123,
            ],
            [
                '[search]' => [
                    'This value should be of type string.',
                ],
            ],
        ];

        yield 'Empty search' => [
            [
                'search' => '',
            ],
            [
                '[search]' => [
                    'This value should not be blank.',
                ],
            ],
        ];

        yield 'Invalid page type' => [
            [
                'page' => 'invalid',
                'page_size' => '10',
            ],
            [
                '[page]' => [
                    'This value should be of type digit.',
                ],
            ],
        ];

        yield 'Page less than 1' => [
            [
                'page' => '0',
                'page_size' => '10',
            ],
            [
                '[page]' => [
                    'Page must be greater than 0.',
                ],
            ],
        ];

        yield 'Page without page_size' => [
            [
                'page' => '1',
            ],
            [
                '[page]' => [
                    'Page size is required when page is provided.',
                ],
            ],
        ];

        yield 'Invalid page_size type' => [
            [
                'page' => '1',
                'page_size' => 'invalid',
            ],
            [
                '[page_size]' => [
                    'This value should be of type digit.',
                ],
            ],
        ];

        yield 'Page_size less than 1' => [
            [
                'page' => '1',
                'page_size' => '0',
            ],
            [
                '[page_size]' => [
                    'Page size must be greater than 0.',
                ],
            ],
        ];

        yield 'Page_size without page' => [
            [
                'page_size' => '10',
            ],
            [
                '[page_size]' => [
                    'Page is required when page size is provided.',
                ],
            ],
        ];
    }
}
