<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Validator\Admin;

use App\Tests\V2\Infrastructure\Validator\ValidatorTestCase;
use App\V2\Infrastructure\Validator\Admin\GetUsersValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use PHPUnit\Framework\Attributes\DataProvider;

class GetUsersValidatorTest extends ValidatorTestCase
{
    /**
     * @throws ValidatorException
     */
    #[DataProvider('validateSuccessProvider')]
    public function testValidateSuccess(array $payload): void
    {
        $this->expectNotToPerformAssertions();

        GetUsersValidator::validateGetUsersRequest($payload);
    }

    public static function validateSuccessProvider(): \Generator
    {
        yield 'Valid page and page_size' => [
            [
                'page' => '1',
                'page_size' => '10',
            ],
        ];

        yield 'Valid is_active (true)' => [
            [
                'is_active' => 'true',
            ],
        ];

        yield 'Valid is_active (false)' => [
            [
                'is_active' => 'false',
            ],
        ];

        yield 'Valid start_date' => [
            [
                'start_date' => '2023-01-01 00:00:00',
            ],
        ];

        yield 'Valid end_date' => [
            [
                'end_date' => '2023-01-01 00:00:00',
            ],
        ];

        yield 'Valid sort_by and sort_order' => [
            [
                'sort_by' => 'name',
                'sort_dir' => 'asc',
            ],
        ];

        yield 'Valid search' => [
            [
                'search' => 'test',
            ],
        ];

        yield 'Valid role' => [
            [
                'role' => 'ROLE_ADMIN',
            ],
        ];

        yield 'Valid all fields' => [
            [
                'page' => '1',
                'page_size' => '10',
                'is_active' => 'true',
                'start_date' => '2023-01-01 00:00:00',
                'end_date' => '2023-01-01 00:00:00',
                'sort_by' => 'name',
                'sort_dir' => 'asc',
                'search' => 'test',
                'role' => 'ROLE_ADMIN',
            ],
        ];
    }

    #[DataProvider('validateFailProvider')]
    public function testValidateFail(array $payload, array $violations): void
    {
        try {
            GetUsersValidator::validateGetUsersRequest($payload);
        } catch (ValidatorException $e) {
            $this->assertViolations($violations, $e->getViolations());
        }
    }

    public static function validateFailProvider(): \Generator
    {
        yield 'Empty page' => [
            [
                'page' => '',
                'page_size' => '10',
            ],
            [
                '[page]' => [
                    'This value should not be blank.',
                    'This value should be of type digit.',
                    'Page must be greater than 0.',
                ],
            ],
        ];

        yield 'page is not a digit' => [
            [
                'page' => 'abc',
                'page_size' => '10',
            ],
            [
                '[page]' => [
                    'This value should be of type digit.',
                ],
            ],
        ];

        yield 'page is less than 1' => [
            [
                'page' => '0',
                'page_size' => '10',
            ],
            [
                '[page]' => [
                    'Page must be greater than 0.',
                ],
            ],
        ];

        yield 'page_size is required when page is provided' => [
            [
                'page' => '1',
            ],
            [
                '[page]' => [
                    'Page size is required when page is provided.',
                ],
            ],
        ];

        yield 'page_size is empty' => [
            [
                'page' => '1',
                'page_size' => '',
            ],
            [
                '[page_size]' => [
                    'This value should not be blank.',
                    'This value should be of type digit.',
                    'Page size must be greater than 0.',
                ],
            ],
        ];

        yield 'page_size is not a digit' => [
            [
                'page' => '1',
                'page_size' => 'abc',
            ],
            [
                '[page_size]' => [
                    'This value should be of type digit.',
                ],
            ],
        ];

        yield 'page_size is less than 1' => [
            [
                'page' => '1',
                'page_size' => '0',
            ],
            [
                '[page_size]' => [
                    'Page size must be greater than 0.',
                ],
            ],
        ];

        yield 'page is required when page_size is provided' => [
            [
                'page_size' => '10',
            ],
            [
                '[page_size]' => [
                    'Page is required when page size is provided.',
                ],
            ],
        ];

        yield 'is_active empty' => [
            [
                'is_active' => '',
            ],
            [
                '[is_active]' => [
                    'This value should not be blank.',
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];

        yield 'is_active invalid value' => [
            [
                'is_active' => 'invalid',
            ],
            [
                '[is_active]' => [
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];

        yield 'start_date empty' => [
            [
                'start_date' => '',
            ],
            [
                '[start_date]' => [
                    'This value should not be blank.',
                ],
            ],
        ];

        yield 'start_date is no string' => [
            [
                'start_date' => 1234567890,
            ],
            [
                '[start_date]' => [
                    'This value should be of type string.',
                    'This value is not a valid datetime.',
                ],
            ],
        ];

        yield 'start_date invalid format' => [
            [
                'start_date' => 'not-a-date',
            ],
            [
                '[start_date]' => [
                    'This value is not a valid datetime.',
                ],
            ],
        ];

        yield 'end_date empty' => [
            [
                'end_date' => '',
            ],
            [
                '[end_date]' => [
                    'This value should not be blank.',
                ],
            ],
        ];

        yield 'end_date is no string' => [
            [
                'end_date' => 1234567890,
            ],
            [
                '[end_date]' => [
                    'This value should be of type string.',
                    'This value is not a valid datetime.',
                ],
            ],
        ];

        yield 'end_date invalid format' => [
            [
                'end_date' => '01/01/2023',
            ],
            [
                '[end_date]' => [
                    'This value is not a valid datetime.',
                ],
            ],
        ];

        yield 'sort_by empty' => [
            [
                'sort_by' => '',
                'sort_dir' => 'asc',
            ],
            [
                '[sort_by]' => [
                    'This value should not be blank.',
                ],
            ],
        ];

        yield 'sort_by is not string' => [
            [
                'sort_by' => 123,
                'sort_dir' => 'asc',
            ],
            [
                '[sort_by]' => [
                    'This value should be of type string.',
                ],
            ],
        ];

        yield 'sort_by without sort_dir' => [
            [
                'sort_by' => 'name',
            ],
            [
                '[sort_by]' => [
                    'Sort direction is required when sort by is provided.',
                ],
            ],
        ];

        yield 'sort_dir empty' => [
            [
                'sort_by' => 'name',
                'sort_dir' => '',
            ],
            [
                '[sort_dir]' => [
                    'This value should not be blank.',
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];

        yield 'sort_dir is not string' => [
            [
                'sort_by' => 'name',
                'sort_dir' => 123,
            ],
            [
                '[sort_dir]' => [
                    'This value should be of type string.',
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];

        yield 'sort_dir without sort_by' => [
            [
                'sort_dir' => 'asc',
            ],
            [
                '[sort_dir]' => [
                    'Sort by is required when sort direction is provided.',
                ],
            ],
        ];

        yield 'sort_dir invalid value' => [
            [
                'sort_by' => 'name',
                'sort_dir' => 'invalid',
            ],
            [
                '[sort_dir]' => [
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];

        yield 'search empty' => [
            [
                'search' => '',
            ],
            [
                '[search]' => [
                    'This value should not be blank.',
                ],
            ],
        ];

        yield 'search is not string' => [
            [
                'search' => 123,
            ],
            [
                '[search]' => [
                    'This value should be of type string.',
                ],
            ],
        ];

        yield 'role empty' => [
            [
                'role' => '',
            ],
            [
                '[role]' => [
                    'This value should not be blank.',
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];

        yield 'role is not string' => [
            [
                'role' => 123,
            ],
            [
                '[role]' => [
                    'This value should be of type string.',
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];

        yield 'role invalid value' => [
            [
                'role' => 'ROLE_INVALID',
            ],
            [
                '[role]' => [
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];

        yield 'multiple validation errors' => [
            [
                'page' => '0',
                'page_size' => 'abc',
                'is_active' => 'invalid',
                'start_date' => 'not-a-date',
                'role' => 'ROLE_INVALID',
            ],
            [
                '[page]' => [
                    'Page must be greater than 0.',
                ],
                '[page_size]' => [
                    'This value should be of type digit.',
                ],
                '[is_active]' => [
                    'The value you selected is not a valid choice.',
                ],
                '[start_date]' => [
                    'This value is not a valid datetime.',
                ],
                '[role]' => [
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];
    }

    public function testValidatePaginatedSearchRequestSuccess(array $payload): void
    {
        $this->expectNotToPerformAssertions();

        GetCreatorsValidator::validateGetCreatorsRequest($payload);
    }
}
