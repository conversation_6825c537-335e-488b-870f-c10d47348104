parameters:
  app.security_config:
    prefix: '/api/v2'
    # Rules order is strict, first found rule is the rule to be applied, by default requires to be authenticated and
    # with the ROLE_ADMIN role to avoid security breaches
    access_control:
      - { path: ^/api/v2/admin/lti, roles: [ ROLE_SUPER_ADMIN ] }
      - { path: '^/api/v2/admin/courses/[\w-]+/creators/[\w-]+', roles: [R<PERSON><PERSON>_SUPER_ADMIN, ROLE_ADMIN, ROLE_CREATOR] }
      - { path: ^/api/v2/admin/users/managers, roles: [ROLE_SUPER_ADMIN, ROLE_ADMIN, ROLE_MANAGER] }
      - { path: ^/api/v2/admin, roles: [ ROLE_ADMIN, ROLE_SUPER_ADMIN, ROLE_MANAGER, ROLE_CREATOR ] }

services:
  _defaults:
    autowire: true      # Automatically injects dependencies in your services.
    autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.
    bind:
      # Log
      $logPath: "%kernel.logs_dir%"

      # JWT
      int $jwtTokenTtl: '%env(JWT_TOKENTTL)%'

      # Database table names
      string $courseCreatorTableName: 'course_creator'
      string $announcementManagerTableName: 'announcement_manager'
      string $purchasableItemTableName: 'purchasable_item'
      string $ltiRegistrationTableName: 'lti_registration'
      string $ltiPlatformTableName: 'lti_platform'
      string $ltiToolTableName: 'lti_tool_v2' # To avoid collision with current lti_tool table
      string $ltiDeploymentTableName: 'lti_deployment'


      ## LTI V2
      string $ltiKeysDir: '%kernel.project_dir%/config/secrets/lti1p3/'

  App\V2\:
    resource: '../../src/V2/'

  App\V2\Application\CommandHandler\:
    resource: '../../src/V2/Application/CommandHandler/'
    tags:
      - { name: 'tactician.handler', typehints: true }

  App\V2\Application\QueryHandler\Admin\:
    resource: '../../src/V2/Application/QueryHandler/Admin/'
    tags:
      - { name: 'tactician.handler', typehints: true }

  App\V2\Infrastructure\Controller\Admin\:
    resource: '../../src/V2/Infrastructure/Controller/Admin/'
    tags: [ 'controller.service_arguments' ]

  ########## V2 Domain repositories ##############
  App\V2\Domain\User\UserRepository:
    class: App\V2\Infrastructure\Persistence\User\DoctrineUserRepository
    arguments:
      - '@doctrine.orm.entity_manager'

  App\V2\Domain\Course\CourseRepository:
    class: App\V2\Infrastructure\Persistence\Course\DoctrineCourseRepository
    arguments:
      - '@doctrine.orm.entity_manager'

  App\V2\Domain\Course\Creator\CourseCreatorRepository:
    alias: App\V2\Infrastructure\Persistence\Course\Creator\DBALCourseCreatorRepository

  App\V2\Domain\Security\RefreshTokenRepository:
    alias: App\V2\Infrastructure\Persistence\Security\DoctrineRefreshTokenRepository

  App\V2\Domain\Announcement\Manager\AnnouncementManagerRepository:
    alias: App\V2\Infrastructure\Persistence\Announcement\Manager\DBALAnnouncementManagerRepository

  App\V2\Domain\Purchase\PurchasableItemRepository:
    alias: App\V2\Infrastructure\Persistence\Purchase\DBALPurchasableItemRepository

  ## LTI Repositories
  App\V2\Domain\LTI\LtiRegistrationRepository:
    alias: App\V2\Infrastructure\Persistence\LTI\DBALLtiRegistrationRepository
  App\V2\Domain\LTI\LtiDeploymentRepository:
    alias: App\V2\Infrastructure\Persistence\LTI\DBALLtiDeploymentRepository
  App\V2\Domain\LTI\LtiToolRepository:
    alias: App\V2\Infrastructure\Persistence\LTI\DBALLtiToolRepository
  App\V2\Domain\LTI\LtiPlatformRepository:
    alias: App\V2\Infrastructure\Persistence\LTI\DBALLtiPlatformRepository


  ######### Hydrators #####################
  App\V2\Application\Hydrator\Course\Creator\CourseCreatorUserHydrator:
  App\V2\Application\Hydrator\Course\Creator\CourseCreatorHydratorCollection:
    autowire: true
    arguments:
      $hydrators:
        - '@App\V2\Application\Hydrator\Course\Creator\CourseCreatorUserHydrator'

  App\V2\Application\Hydrator\Announcement\Manager\AnnouncementManagerHydrator:
  App\V2\Application\Hydrator\Announcement\Manager\AnnouncementManagerHydratorCollection:
    autowire: true
    arguments:
      $hydrators:
        - '@App\V2\Application\Hydrator\Announcement\Manager\AnnouncementManagerHydrator'

  App\V2\Application\Hydrator\LTI\LtiRegistrationDeploymentHydrator:
  App\V2\Application\Hydrator\LTI\LtiRegistrationPlatformHydrator:
  App\V2\Application\Hydrator\LTI\LtiRegistrationToolHydrator:
  App\V2\Application\Hydrator\LTI\LtiRegistrationHydratorCollection:
    arguments:
      - - '@App\V2\Application\Hydrator\LTI\LtiRegistrationDeploymentHydrator'
        - '@App\V2\Application\Hydrator\LTI\LtiRegistrationPlatformHydrator'
        - '@App\V2\Application\Hydrator\LTI\LtiRegistrationToolHydrator'


  ######## Services ###############
  App\V2\Application\Log\Logger:
    alias: 'App\V2\Infrastructure\Log\MonologLogger'

  App\V2\Infrastructure\Utils\MpdfFactory:
    arguments:
      $cacheDir: "%kernel.cache_dir%"

  # Security
  App\V2\Infrastructure\Security\Firewall:
    arguments:
      $security: '%app.security_config%'
      $routeChecker: '@App\V2\Infrastructure\Service\RouteMethodChecker'
      $roleHierarchy: '%security.role_hierarchy.roles%'
    public: true

  ## LTI V2
  App\V2\Infrastructure\LTI\OpenSSLKeyProvider:
  App\V2\Domain\LTI\LtiKeyProvider:
    alias: App\V2\Infrastructure\LTI\OpenSSLKeyProvider


  App\V2\Infrastructure\Security\LexitJwtToken:
  App\V2\Domain\Security\TokenInterface:
    alias: App\V2\Infrastructure\Security\LexitJwtToken
