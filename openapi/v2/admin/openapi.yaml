openapi: 3.0.0
info:
  title: Easylearning Admin API v2
  description: API for Easylearning administration
  version: 2.0.0

servers:
  - url: /api/v2/admin
    description: Admin server v2

security:
  - bearerAuth: []

paths:
  /users:
    get:
      tags:
        - Users
      summary: Get users list
      description: |
        Retrieves a paginated list of users with filtering capabilities.
        Filters work as AND between different categories and OR within the same category.
        If the requesting user is a manager and not an admin, users will be filtered
        according to their assigned filters, also adding users they have created.
      operationId: getUsers
      parameters:
        - name: page
          in: query
          description: Page number (starts at 1)
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: page_size
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
            default: 10
        - name: is_active
          in: query
          description: Filter by active status
          required: false
          schema:
            type: string
            enum: [true, false]
        - name: start_date
          in: query
          description: Filter by start date (format Y-m-d H:i:s)
          required: false
          schema:
            type: string
            format: date-time
        - name: end_date
          in: query
          description: Filter by end date (format Y-m-d H:i:s)
          required: false
          schema:
            type: string
            format: date-time
        - name: search
          in: query
          description: Search by text (first name, last name, email)
          required: false
          schema:
            type: string
        - name: role
          in: query
          description: Filter by role
          required: false
          schema:
            type: string
        - name: sort_by
          in: query
          description: Field to sort by (id, first_name, email)
          required: false
          schema:
            type: string
            enum: [id, first_name, email]
            default: id
        - name: sort_dir
          in: query
          description: Sort direction
          required: false
          schema:
            type: string
            enum: [asc, desc]
            default: asc
      responses:
        '200':
          description: Paginated list of users
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedUserCollection'
        '400':
          description: Invalid query parameters
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Insufficient permissions
  /users/creators:
    get:
      tags:
        - Users
      summary: Get users with role CREATOR
      description: |
        Returns a list of users that have the role CREATOR.
        This endpoint is accessible by users with roles: ADMIN, SUPER_ADMIN, or CREATOR.
      operationId: getCreatorUsers
      responses:
        '200':
          description: List of users with role CREATOR
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MinimalUser'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Insufficient permissions
  /users/managers:
    get:
      tags:
        - Users
      summary: Get users with role MANAGER
      description: |
        Returns a paginated list of users that have the role MANAGER.
        Users are sorted alphabetically by first name and then by last name.
        This endpoint is accessible by users with roles: ADMIN, SUPER_ADMIN, or MANAGER.
      operationId: getManagerUsers
      parameters:
        - name: search
          in: query
          description: Search by text (first name, last name, email)
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Page number (starts at 1)
          required: false
          schema:
            type: integer
            minimum: 1
        - name: page_size
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
      responses:
        '200':
          description: Paginated list of users with role MANAGER
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      users:
                        type: array
                        items:
                          $ref: '#/components/schemas/MinimalUser'
                      total:
                        type: integer
                        description: Total number of managers
        '400':
          description: Invalid query parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Insufficient permissions
  /courses/{courseId}/creators:
    get:
      tags:
        - Courses
      summary: Get all creators of a course
      description: Retrieve a normal list of basic user information as creator for a course
      operationId: getCourseCreators
      parameters:
        - name: courseId
          in: path
          description: Course ID
          required: true
          schema:
            type: integer
            minimum: 1
      responses:
        200:
          description: List of course creators
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Creator'
  /courses/{courseId}/creators/{userId}:
    put:
      tags:
        - Courses
      summary: Add a creator to a course
      description: |
        Adds a creator (user with ROLE_CREATOR) to a course.
        Only administrators and the main creator of the course can perform this action.
        The user to be added must have the ROLE_CREATOR role.
      operationId: putCourseCreator
      parameters:
        - name: courseId
          in: path
          description: Course ID
          required: true
          schema:
            type: integer
            minimum: 1
        - name: userId
          in: path
          description: User ID (must be a creator)
          required: true
          schema:
            type: integer
            minimum: 1
      responses:
        '204':
          description: Creator successfully added to course
        '400':
          description: Validation errors (invalid courseId or userId)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - User is not authorized to add creators to this course
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Course or user not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '422':
          description: Unprocessable entity
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                creatorNotFound:
                  summary: User is not a creator
                  value:
                    message: "User is not a creator"
                creatorAlreadyExists:
                  summary: Creator already associated with course
                  value:
                    message: "Creator is already associated with this course"
        '500':
          description: Internal server error
    delete:
      tags:
        - Courses
      summary: Delete a course creator
      description: Remove a creator from a course. Only admins and the course creator can use this endpoint.
      operationId: deleteCourseCreator
      parameters:
        - name: courseId
          in: path
          description: Course ID
          required: true
          schema:
            type: integer
            minimum: 1
        - name: userId
          in: path
          description: User ID of the creator to remove
          required: true
          schema:
            type: integer
            minimum: 1
      responses:
        204:
          description: Course creator deleted successfully
        400:
          description: Validation errors
        401:
          description: Unauthorized
        403:
          description: Forbidden - Insufficient permissions
        404:
          description: Course not found
        422:
          description: Unprocessable entity - CourseCreator not found
        5XX:
          description: Internal server error

  /announcements/{announcementId}/managers:
    get:
      tags:
        - Announcements
      summary: Get all managers of an announcement
      description: |
        Retrieve a list of managers associated with a specific announcement.
        Returns managers sorted alphabetically by name (first name + last name).
        This endpoint is only available when the 'app.announcement.managers.sharing' setting is enabled.
      operationId: getAnnouncementManagers
      parameters:
        - name: announcementId
          in: path
          description: Announcement ID to get the associated managers
          required: true
          schema:
            type: integer
            minimum: 1
      responses:
        200:
          description: List of announcement managers sorted alphabetically
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/AnnouncementManager'
        400:
          description: Validation errors (invalid announcementId)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        401:
          description: Unauthorized
        403:
          description: Forbidden - Announcement manager sharing is disabled
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                message: "Announcement manager sharing is disabled"
        404:
          description: Announcement not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                message: "Announcement not found"
        500:
          description: Internal server error

  /lti/registrations:
    options:
      tags:
        - LTI
      summary: Check if the endpoint is available
      responses:
        204:
          description: LTI Registrations endpoint is available
    post:
      tags:
        - LTI
      summary: POST a registration entity
      description: Save a new registration
      operationId: postLtiRegistration
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  required: true
                client_id:
                  type: string
                  required: true
      responses:
        201:
          description: 'LTI Registration created'
        400:
          description: 'Body validation failed'
        401:
          description: 'Unauthorized'
        403:
          description: 'Forbidden'
        422:
          description: 'Unprocessable entity'
        5XX:
          description: 'Internal server error'

  /lti/registrations/{registrationId}/tool:
    parameters:
      - name: registrationId
        in: path
        required: true
        schema:
          $ref: '#/components/schemas/Uuid'
    options:
      tags:
        - LTI
      summary: Check if the endpoint is available
      responses:
        204:
          description: LTI Tool endpoint is available
    post:
      tags:
        - LTI
      summary: POST Lti Tool
      description: Save tool registry for a registration
      operationId: postLtiTool
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  required: true
                audience:
                  type: string
                  required: true
                oidc_initiation_url:
                  $ref: '#/components/schemas/Url'
                launch_url:
                  $ref: '#/components/schemas/Url'
                deep_linking_url:
                  $ref: '#/components/schemas/Url'
                jwks_url:
                  $ref: '#/components/schemas/Url'
      responses:
        201:
          description: Lti Tool created
        400:
          description: Body validation failed
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          description: Lti Registration not found
        5XX:
          description: Internal Server Error
  /lti/registrations/{registrationId}/platform:
    parameters:
      - name: registrationId
        in: path
        required: true
        schema:
          $ref: '#/components/schemas/Uuid'
    options:
      tags:
        - LTI
      summary: Check if the endpoint is available
      responses:
        204:
          description: LTI Platform endpoint is available
    post:
      tags:
        - LTI
      summary: POST Lti Platform
      description: Save platform registry for a registration
      operationId: postLtiPlatform
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  required: true
                audience:
                  type: string
                  required: true
                oidc_authentication_url:
                  $ref: '#/components/schemas/Url'
                oauth2_access_token_url:
                  $ref: '#/components/schemas/Url'
                jwks_url:
                  $ref: '#/components/schemas/Url'
      responses:
        201:
          description: Lti Platform created
        400:
          description: Body validation failed
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          description: Lti Registration not found
        5XX:
          description: Internal Server Error

components:
  schemas:
    Uuid:
      type: string
      format: uuid
      example: "550e8400-e29b-41d4-a716-************"
      description: "UUID v4"
    Url:
      type: string
      format: url
      example: "https://example.com"
      description: A valid url
    Creator:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: User's unique ID
        name:
          type: string
          description: User's first name
        lastName:
          type: string
          description: User's last name
        email:
          type: string
          format: email
          description: User's email address
    AnnouncementManager:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: Manager's unique ID
        name:
          type: string
          description: Manager's first name
        lastName:
          type: string
          description: Manager's last name
        email:
          type: string
          format: email
          description: Manager's email address
      required:
        - id
        - name
        - lastName
        - email
    User:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: User's unique ID
        firstName:
          type: string
          description: User's first name
        lastName:
          type: string
          description: User's last name
        email:
          type: string
          format: email
          description: User's email address
        roles:
          type: array
          items:
            type: string
          description: Roles assigned to the user
        isActive:
          type: boolean
          description: Indicates if the user is active
        createdAt:
          type: string
          format: date-time
          description: User creation date
        updatedAt:
          type: string
          format: date-time
          description: User last update date
        actions:
          type: object
          description: Available actions for the user based on permissions
    MinimalUser:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: User's unique ID
        first_name:
          type: string
          description: User's first name
        last_name:
          type: string
          description: User's last name
        email:
          type: string
          format: email
          description: User's email address
    PaginatedUserCollection:
      type: object
      properties:
        data:
          type: object
          properties:
            items:
              type: array
              items:
                $ref: '#/components/schemas/User'
            pagination:
              type: object
              properties:
                total:
                  type: integer
                  description: Total number of items
                page:
                  type: integer
                  description: Current page
                pageSize:
                  type: integer
                  description: Page size
                totalPages:
                  type: integer
                  description: Total number of pages
    Error:
      type: object
      properties:
        message:
          type: string
          description: Error message
    ValidationError:
      type: object
      properties:
        message:
          type: string
          description: Validation error message
        metadata:
          type: object
          properties:
            violations:
              type: object
              additionalProperties:
                type: string
              description: Field validation violations

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
